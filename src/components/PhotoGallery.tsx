"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Heart, Download, Share2 } from "lucide-react";

interface Photo {
  id: string;
  style: string;
  imageUrl: string;
  description: string;
}

interface PhotoGalleryProps {
  photos: Photo[];
  title?: string;
  subtitle?: string;
}

export function PhotoGallery({ photos, title, subtitle }: PhotoGalleryProps) {
  const [favorites, setFavorites] = useState<string[]>([]);
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null);

  const toggleFavorite = (photoId: string) => {
    setFavorites(prev => 
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    );
  };

  const handleDownload = (photo: Photo) => {
    // In a real app, this would download the actual image
    const link = document.createElement('a');
    link.href = photo.imageUrl;
    link.download = `wedding-photo-${photo.style}-${photo.id}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = (photo: Photo) => {
    if (navigator.share) {
      navigator.share({
        title: `Beautiful ${photo.style} Wedding Photo`,
        text: photo.description,
        url: window.location.href,
      });
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  return (
    <div className="w-full">
      {(title || subtitle) && (
        <div className="text-center mb-12">
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-gray-600 text-lg">{subtitle}</p>}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {photos.map((photo) => (
          <Card key={photo.id} className="overflow-hidden hover:shadow-lg transition-shadow group">
            <div className="relative">
              <img
                src={photo.imageUrl}
                alt={`${photo.style} wedding photo`}
                className="w-full h-64 object-cover cursor-pointer"
                onClick={() => setSelectedPhoto(photo)}
              />
              
              {/* Overlay with actions */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleFavorite(photo.id);
                    }}
                    className={`${
                      favorites.includes(photo.id)
                        ? "bg-pink-500 text-white"
                        : "bg-white text-gray-700"
                    }`}
                  >
                    <Heart className="h-4 w-4" fill={favorites.includes(photo.id) ? "currentColor" : "none"} />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDownload(photo);
                    }}
                    className="bg-white text-gray-700"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleShare(photo);
                    }}
                    className="bg-white text-gray-700"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="p-4">
              <h3 className="font-semibold mb-2">{photo.style}</h3>
              <p className="text-gray-600 text-sm">{photo.description}</p>
            </div>
          </Card>
        ))}
      </div>

      {/* Modal for full-size photo view */}
      {selectedPhoto && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setSelectedPhoto(null)}
        >
          <div className="max-w-4xl max-h-full relative">
            <img
              src={selectedPhoto.imageUrl}
              alt={`${selectedPhoto.style} wedding photo`}
              className="max-w-full max-h-full object-contain"
            />
            <button
              className="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-8 h-8 flex items-center justify-center hover:bg-opacity-75"
              onClick={() => setSelectedPhoto(null)}
            >
              ×
            </button>
            <div className="absolute bottom-4 left-4 right-4 text-white">
              <h3 className="text-xl font-semibold mb-2">{selectedPhoto.style}</h3>
              <p className="text-gray-200">{selectedPhoto.description}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
