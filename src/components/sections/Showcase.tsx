"use client";

import { PhotoGallery } from "@/components/PhotoGallery";

interface ShowcaseProps {
  section: {
    title: string;
    subtitle: string;
    gallery: Array<{
      image: string;
      title: string;
      description: string;
    }>;
  };
}

export function Showcase({ section }: ShowcaseProps) {
  // Convert gallery items to photo format
  const photos = section.gallery.map((item, index) => ({
    id: `showcase-${index}`,
    style: item.title,
    imageUrl: item.image,
    description: item.description,
  }));

  return (
    <section id="showcase" className="w-full py-16 md:py-24 lg:py-32">
      <div className="container mx-auto px-4 md:px-6">
        <PhotoGallery
          photos={photos}
          title={section.title}
          subtitle={section.subtitle}
        />
      </div>
    </section>
  );
}
