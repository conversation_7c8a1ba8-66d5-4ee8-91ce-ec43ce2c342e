"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface CTAProps {
  section: {
    title: string;
    subtitle: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
}

export function CTA({ section }: CTAProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  return (
    <section className="py-20 bg-gradient-to-r from-pink-500 via-rose-500 to-purple-600 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-white/10 rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-white/5 rounded-full blur-2xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 text-white text-sm font-medium mb-6">
            💝 {currentLocale === 'zh' ? '开始您的婚纱照之旅' : 'Start Your Wedding Photo Journey'}
          </div>

          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">{section.title}</h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">{section.subtitle}</p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <Link href={`/${currentLocale}/upload`}>
              <Button size="lg" className="bg-white text-pink-600 hover:bg-gray-100 rounded-full px-8 h-12 font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                {section.cta.primary}
              </Button>
            </Link>
            <Link href={`/${currentLocale}/results`}>
              <Button
                size="lg"
                variant="outline"
                className="rounded-full px-8 h-12 border-2 border-white text-white hover:bg-white hover:text-pink-600 font-semibold transition-all duration-300 transform hover:scale-105"
              >
                {section.cta.secondary}
              </Button>
            </Link>
          </div>

          {/* 添加信任标识 */}
          <div className="flex flex-wrap items-center justify-center gap-8 text-white/80 text-sm">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-400 rounded-full"></span>
              {currentLocale === 'zh' ? '100,000+ 用户信赖' : '100,000+ trusted users'}
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
              {currentLocale === 'zh' ? '24小时隐私保护' : '24-hour privacy protection'}
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
              {currentLocale === 'zh' ? '专业4K画质' : 'Professional 4K quality'}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
