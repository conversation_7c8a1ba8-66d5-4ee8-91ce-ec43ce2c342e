"use client";

import Image from 'next/image';

interface Feature1Props {
  section: {
    title: string;
    subtitle: string;
    description: string;
    image: string;
    features: Array<{
      title: string;
      description: string;
      icon: string;
    }>;
  };
}

export function Feature1({ section }: Feature1Props) {
  return (
    <section id="introduce" className="py-20 bg-gradient-to-b from-white to-pink-50 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <div className="inline-flex items-center px-3 py-1 rounded-full bg-pink-100 text-pink-700 text-sm font-medium mb-4">
              🤖 AI Technology
            </div>
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-pink-600 to-rose-600 bg-clip-text text-transparent">{section.title}</h2>
            <p className="text-xl text-gray-600 mb-6 font-medium">{section.subtitle}</p>
            <p className="text-gray-600 mb-8 leading-relaxed">{section.description}</p>
            <div className="space-y-6">
              {section.features.map((feature, index) => (
                <div key={index} className="flex gap-4 p-4 rounded-lg bg-white dark:bg-gray-800 shadow-sm border border-pink-100 dark:border-gray-700 hover:shadow-md transition-shadow">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-r from-pink-500 to-rose-500 flex items-center justify-center text-white">
                      {index === 0 && '🎨'}
                      {index === 1 && '✨'}
                      {index === 2 && '⚡'}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
                    <p className="text-gray-600 dark:text-gray-300">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-pink-400 to-rose-400 rounded-lg transform rotate-3 opacity-20"></div>
            <div className="relative bg-white dark:bg-gray-800 rounded-lg p-8 shadow-xl">
              <div className="text-center">
                <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-pink-500 to-rose-500 rounded-full flex items-center justify-center text-white text-3xl">
                  👰
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">AI婚纱照生成流程</h3>
                <div className="space-y-4 text-left">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 font-bold text-sm">1</div>
                    <span className="text-gray-600 dark:text-gray-300">上传清晰自拍照</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 font-bold text-sm">2</div>
                    <span className="text-gray-600 dark:text-gray-300">选择喜欢的风格</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 font-bold text-sm">3</div>
                    <span className="text-gray-600 dark:text-gray-300">AI智能生成婚纱照</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 font-bold text-sm">4</div>
                    <span className="text-gray-600 dark:text-gray-300">下载高清作品</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
