"use client";

import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface HeroProps {
  hero: {
    title: string;
    subtitle: string;
    description: string;
    cta: {
      primary: string;
      secondary: string;
    };
  };
}

export function Hero({ hero }: HeroProps) {
  const pathname = usePathname();
  const currentLocale = pathname.split('/')[1];

  return (
    <section className="relative flex items-center justify-center min-h-[calc(100vh-4rem)] w-full py-12 md:py-24 lg:py-32 overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-pink-50 via-white to-rose-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"></div>
      <div className="absolute top-20 left-10 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"></div>
      <div className="absolute top-40 right-10 w-72 h-72 bg-rose-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-1000"></div>
      <div className="absolute bottom-20 left-1/2 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse delay-2000"></div>

      <div className="container px-4 md:px-6 mx-auto relative z-10">
        <div className="flex flex-col items-center justify-center space-y-8 text-center">
          <div className="space-y-6 max-w-4xl mx-auto">
            {/* 添加小标签 */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-pink-100 to-rose-100 text-pink-700 text-sm font-medium">
              ✨ {currentLocale === 'zh' ? '专业AI婚纱照生成' : 'Professional AI Wedding Photography'}
            </div>

            <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl lg:text-7xl bg-gradient-to-r from-pink-600 via-rose-600 to-purple-600 bg-clip-text text-transparent">
              {hero.title}
            </h1>
            <p className="text-xl text-gray-600 md:text-2xl dark:text-gray-300 font-medium">
              {hero.subtitle}
            </p>
            <p className="text-lg text-gray-500 md:text-xl dark:text-gray-400 max-w-3xl mx-auto leading-relaxed">
              {hero.description}
            </p>
          </div>

          <div className="flex flex-wrap items-center justify-center gap-4">
            <Link href={`/${currentLocale}/upload`}>
              <Button size="lg" className="min-w-[180px] h-12 bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                {hero.cta.primary}
              </Button>
            </Link>
            <Link href={`/${currentLocale}/results`}>
              <Button variant="outline" size="lg" className="min-w-[180px] h-12 border-2 border-pink-500 text-pink-600 hover:bg-pink-50 dark:hover:bg-pink-950 font-semibold rounded-full transition-all duration-300 transform hover:scale-105">
                {hero.cta.secondary}
              </Button>
            </Link>
          </div>

          {/* 添加特色标签 */}
          <div className="flex flex-wrap items-center justify-center gap-6 mt-8 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-green-500 rounded-full"></span>
              {currentLocale === 'zh' ? '3分钟快速生成' : '3-minute generation'}
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
              {currentLocale === 'zh' ? '4K专业画质' : '4K professional quality'}
            </div>
            <div className="flex items-center gap-2">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              {currentLocale === 'zh' ? '6种精美风格' : '6 beautiful styles'}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
