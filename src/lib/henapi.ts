// HenAPI service for image generation
// Provides integration with https://www.henapi.top/v1/images/generations

// Ensure fetch is available in the environment
// Next.js provides fetch polyfill in API routes

interface HenAPIGenerationRequest {
  model: string;
  prompt: string;
  n: number;
  size: string;
}

interface HenAPIGenerationResponse {
  data: Array<{
    url: string;
  }>;
}

export class HenAPIService {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string = "https://www.henapi.top/v1") {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async generateImage(params: HenAPIGenerationRequest): Promise<HenAPIGenerationResponse> {
    const headers = new Headers();
    headers.append("Authorization", `Bearer ${this.apiKey}`);
    headers.append("Content-Type", "application/json");

    const requestBody = JSON.stringify({
      model: params.model,
      prompt: params.prompt,
      n: params.n,
      size: params.size
    });

    const requestOptions: RequestInit = {
      method: 'POST',
      headers: headers,
      body: requestBody,
      redirect: 'follow',
      // 添加超时设置
      signal: AbortSignal.timeout(60000) // 60秒超时
    };

    const endpoint = `${this.baseURL}/images/generations`;

    // 详细的请求日志
    console.log('🔍 [HenAPI] Request Details:');
    console.log('  📍 Endpoint:', endpoint);
    console.log('  🔑 API Key:', this.apiKey ? `${this.apiKey.substring(0, 8)}...${this.apiKey.slice(-4)}` : 'NOT_SET');
    console.log('  📋 Headers:', {
      'Authorization': `Bearer ${this.apiKey ? this.apiKey.substring(0, 8) + '...' + this.apiKey.slice(-4) : 'NOT_SET'}`,
      'Content-Type': 'application/json'
    });
    console.log('  📦 Request Body:', {
      model: params.model,
      prompt: params.prompt.substring(0, 100) + (params.prompt.length > 100 ? '...' : ''),
      n: params.n,
      size: params.size
    });

    try {
      // Check if fetch is available
      if (typeof fetch === 'undefined') {
        throw new Error('fetch is not available in this environment. Please ensure you are running in a Next.js API route or upgrade to Node.js 18+');
      }

      console.log('🚀 [HenAPI] Sending request to:', endpoint);
      const startTime = Date.now();

      const response = await fetch(endpoint, requestOptions);

      const duration = Date.now() - startTime;
      console.log(`⏱️ [HenAPI] Request completed in ${duration}ms`);
      console.log('📥 [HenAPI] Response Status:', response.status, response.statusText);
      console.log('📥 [HenAPI] Response Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.log('❌ [HenAPI] Error Response Body:', errorText);
        throw new Error(`HenAPI request failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ [HenAPI] Success Response:', {
        created: result.created,
        dataLength: result.data?.length || 0,
        hasImageUrl: !!(result.data?.[0]?.url),
        imageUrlPreview: result.data?.[0]?.url ? result.data[0].url.substring(0, 80) + "..." : "N/A",
        hasRevisedPrompt: !!(result.data?.[0]?.revised_prompt),
        revisedPromptPreview: result.data?.[0]?.revised_prompt ? result.data[0].revised_prompt.substring(0, 100) + "..." : "N/A"
      });

      return result as HenAPIGenerationResponse;
    } catch (error) {
      console.log('❌ [HenAPI] Request failed with error:', error);

      if (error instanceof Error) {
        console.log('❌ [HenAPI] Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack?.substring(0, 500)
        });

        // 提供更详细的错误信息
        if (error.message.includes('fetch failed')) {
          console.log('💡 [HenAPI] Network error detected. Possible causes:');
          console.log('  - Network connectivity issues');
          console.log('  - DNS resolution problems');
          console.log('  - Firewall blocking the request');
          console.log('  - HenAPI service is down');
          console.log('  - SSL/TLS certificate issues');
        }

        throw new Error(`HenAPI generation failed: ${error.message}`);
      }
      throw new Error('HenAPI generation failed: Unknown error');
    }
  }
}

// Factory function to create HenAPI service instance
export function createHenAPIService(): HenAPIService | null {
  const apiKey = process.env.HENAPI_API_KEY;
  const baseURL = process.env.HENAPI_BASE_URL || "https://www.henapi.top/v1";

  if (!apiKey) {
    console.error('HENAPI_API_KEY not configured');
    return null;
  }

  return new HenAPIService(apiKey, baseURL);
}
